# pyright: basic
import numpy as np
from sweeper400.analyze import (
    SamplingInfo,
    sine_wave_vvi,
    init_sampling_info,
    extract_single_tone_information_vvi,
    Waveform,
)

sampling_info = init_sampling_info(68600, 34300)

testwave = sine_wave_vvi(
    sampling_info,
    frequency=3430.6,
    amplitude=1.68,
    phase=3.1,
)

output = extract_single_tone_information_vvi(testwave, approx_freq=3430.0)
print(output)
