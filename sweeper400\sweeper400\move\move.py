"""
步进电机控制模块

该模块提供步进电机控制功能，包括：
- 电机参数获取
- 脉冲数与物理长度的换算
- 电机运动控制

主要功能：
1. 获取电机硬件参数
2. 建立脉冲数与物理长度(mm)的换算关系
3. 提供安全的电机控制接口
"""

import os
from ctypes import *
from typing import Optional
import logging

# 导入日志系统
from ..logger import get_logger

# 获取模块专用日志器
logger = get_logger(__name__)


class MotorController:
    """步进电机控制器类

    该类封装了MT-22E控制器的API调用，提供安全的电机控制功能。
    主要功能包括参数获取、单位换算和运动控制。
    """

    def __init__(self):
        """初始化电机控制器"""
        self._dll_path = os.path.join(os.path.dirname(__file__), "MT_API.dll")
        self._api = None
        self._is_initialized = False
        self._is_connected = False

        # 电机参数（需要通过API或手动设置获取）
        self._step_angle: Optional[float] = None  # 步进角度（度）
        self._subdivision: Optional[int] = None  # 细分数
        self._pitch: Optional[float] = None  # 螺距（mm）
        self._transmission_ratio: float = 1.0  # 传动比（默认无传动）

        logger.info("电机控制器实例已创建")

    def initialize(self) -> bool:
        """初始化API库

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 加载DLL
            self._api = windll.LoadLibrary(self._dll_path)
            logger.info(f"成功加载DLL: {self._dll_path}")

            # 设置函数参数类型
            self._setup_api_types()

            # 初始化API
            result = self._api.MT_Init()
            if result == 0:
                self._is_initialized = True
                logger.info("API初始化成功")
                return True
            else:
                logger.error(f"API初始化失败，错误码: {result}")
                return False

        except Exception as e:
            logger.error(f"初始化过程中发生异常: {e}")
            return False

    def _setup_api_types(self):
        """设置API函数的参数类型"""
        # 硬件信息相关函数
        self._api.MT_Get_Product_Resource.argtypes = [POINTER(c_int32)]
        self._api.MT_Get_Product_ID.argtypes = [c_char_p]
        self._api.MT_Get_Product_SN.argtypes = [c_char_p]

        # 辅助计算函数
        self._api.MT_Help_Step_Line_Real_To_Steps.argtypes = [
            c_double,
            c_int32,
            c_double,
            c_double,
            c_double,
        ]
        self._api.MT_Help_Step_Line_Real_To_Steps.restype = c_int32

        self._api.MT_Help_Step_Line_Steps_To_Real.argtypes = [
            c_double,
            c_int32,
            c_double,
            c_double,
            c_int32,
        ]
        self._api.MT_Help_Step_Line_Steps_To_Real.restype = c_double

        # 通信相关函数
        self._api.MT_Open_UART.argtypes = [c_char_p]
        self._api.MT_Open_USB.argtypes = []

        logger.debug("API函数参数类型设置完成")

    def connect_usb(self) -> bool:
        """通过USB连接控制器

        Returns:
            bool: 连接是否成功
        """
        if not self._is_initialized:
            logger.error("API未初始化，无法连接")
            return False

        try:
            result = self._api.MT_Open_USB()
            if result == 0:
                # 检查连接状态
                check_result = self._api.MT_Check()
                if check_result == 0:
                    self._is_connected = True
                    logger.info("USB连接成功")
                    return True
                else:
                    logger.error(f"连接检查失败，错误码: {check_result}")
                    return False
            else:
                logger.error(f"USB连接失败，错误码: {result}")
                return False

        except Exception as e:
            logger.error(f"USB连接过程中发生异常: {e}")
            return False

    def get_hardware_info(self) -> dict[str, str | int]:
        """获取硬件信息

        Returns:
            dict: 包含硬件信息的字典
        """
        if not self._is_connected:
            logger.error("未连接到控制器，无法获取硬件信息")
            return {}

        info = {}

        try:
            # 获取产品资源信息
            resource = c_int32(0)
            result = self._api.MT_Get_Product_Resource(byref(resource))
            if result == 0:
                info["resource"] = resource.value
                logger.info(f"产品资源信息: 0x{resource.value:08X}")
            else:
                logger.warning(f"获取产品资源失败，错误码: {result}")

            # 获取产品ID
            product_id = create_string_buffer(16)
            result = self._api.MT_Get_Product_ID(product_id)
            if result == 0:
                info["product_id"] = product_id.value.decode("gbk")
                logger.info(f"产品ID: {info['product_id']}")
            else:
                logger.warning(f"获取产品ID失败，错误码: {result}")

            # 获取产品序列号
            serial_number = create_string_buffer(12)
            result = self._api.MT_Get_Product_SN(serial_number)
            if result == 0:
                info["serial_number"] = serial_number.value.decode("gbk")
                logger.info(f"产品序列号: {info['serial_number']}")
            else:
                logger.warning(f"获取产品序列号失败，错误码: {result}")

        except Exception as e:
            logger.error(f"获取硬件信息时发生异常: {e}")

        return info

    def set_motor_parameters(
        self,
        step_angle: float,
        subdivision: int,
        pitch: float,
        transmission_ratio: float = 1.0,
    ):
        """设置电机参数

        Args:
            step_angle: 步进角度（度），通常为1.8°
            subdivision: 驱动器细分数，如2,4,8,16,32等
            pitch: 螺距（mm），电机旋转一圈的位移
            transmission_ratio: 传动比，无传动时为1.0
        """
        self._step_angle = step_angle
        self._subdivision = subdivision
        self._pitch = pitch
        self._transmission_ratio = transmission_ratio

        logger.info(
            f"电机参数已设置: 步进角度={step_angle}°, 细分数={subdivision}, "
            f"螺距={pitch}mm, 传动比={transmission_ratio}"
        )

    def get_motor_parameters(self) -> dict[str, float | int | None]:
        """获取当前电机参数

        Returns:
            dict: 包含电机参数的字典
        """
        return {
            "step_angle": self._step_angle,
            "subdivision": self._subdivision,
            "pitch": self._pitch,
            "transmission_ratio": self._transmission_ratio,
        }

    def _check_parameters(self) -> bool:
        """检查电机参数是否已设置

        Returns:
            bool: 参数是否完整
        """
        if self._step_angle is None or self._subdivision is None or self._pitch is None:
            logger.error("电机参数未完整设置，无法进行换算")
            return False
        return True

    def mm_to_steps(self, distance_mm: float) -> int:
        """将物理距离（mm）转换为脉冲数

        Args:
            distance_mm: 物理距离（mm）

        Returns:
            int: 对应的脉冲数

        Raises:
            ValueError: 当参数未设置时
        """
        if not self._check_parameters():
            raise ValueError("电机参数未完整设置")

        if not self._is_connected:
            logger.warning("未连接到控制器，使用本地计算")

        try:
            # 确保参数不为None（已通过_check_parameters检查）
            assert self._step_angle is not None
            assert self._subdivision is not None
            assert self._pitch is not None

            if self._is_connected and self._api is not None:
                # 使用API函数进行转换
                steps = self._api.MT_Help_Step_Line_Real_To_Steps(
                    self._step_angle,
                    self._subdivision,
                    self._pitch,
                    self._transmission_ratio,
                    distance_mm,
                )
                logger.debug(f"API转换: {distance_mm}mm -> {steps}步")
            else:
                # 本地计算：steps = (distance_mm / pitch) * (360 / step_angle) * subdivision * transmission_ratio
                steps_per_revolution = (360.0 / self._step_angle) * self._subdivision
                steps = int(
                    (distance_mm / self._pitch)
                    * steps_per_revolution
                    * self._transmission_ratio
                )
                logger.debug(f"本地计算: {distance_mm}mm -> {steps}步")

            return steps

        except Exception as e:
            logger.error(f"距离转换脉冲数时发生异常: {e}")
            raise

    def steps_to_mm(self, steps: int) -> float:
        """将脉冲数转换为物理距离（mm）

        Args:
            steps: 脉冲数

        Returns:
            float: 对应的物理距离（mm）

        Raises:
            ValueError: 当参数未设置时
        """
        if not self._check_parameters():
            raise ValueError("电机参数未完整设置")

        if not self._is_connected:
            logger.warning("未连接到控制器，使用本地计算")

        try:
            # 确保参数不为None（已通过_check_parameters检查）
            assert self._step_angle is not None
            assert self._subdivision is not None
            assert self._pitch is not None

            if self._is_connected and self._api is not None:
                # 使用API函数进行转换
                distance = self._api.MT_Help_Step_Line_Steps_To_Real(
                    self._step_angle,
                    self._subdivision,
                    self._pitch,
                    self._transmission_ratio,
                    steps,
                )
                logger.debug(f"API转换: {steps}步 -> {distance}mm")
            else:
                # 本地计算：distance = (steps / steps_per_revolution) * pitch / transmission_ratio
                steps_per_revolution = (360.0 / self._step_angle) * self._subdivision
                distance = (
                    (steps / steps_per_revolution)
                    * self._pitch
                    / self._transmission_ratio
                )
                logger.debug(f"本地计算: {steps}步 -> {distance}mm")

            return distance

        except Exception as e:
            logger.error(f"脉冲数转换距离时发生异常: {e}")
            raise

    def get_conversion_factor(self) -> float:
        """获取换算系数（步/mm）

        Returns:
            float: 每毫米对应的脉冲数

        Raises:
            ValueError: 当参数未设置时
        """
        if not self._check_parameters():
            raise ValueError("电机参数未完整设置")

        # 确保参数不为None（已通过_check_parameters检查）
        assert self._step_angle is not None
        assert self._subdivision is not None
        assert self._pitch is not None

        # 计算每毫米的脉冲数
        steps_per_revolution = (360.0 / self._step_angle) * self._subdivision
        steps_per_mm = (steps_per_revolution * self._transmission_ratio) / self._pitch

        logger.info(f"换算系数: {steps_per_mm:.2f} 步/mm")
        return steps_per_mm

    def disconnect(self):
        """断开连接"""
        if self._is_connected and self._api:
            try:
                self._api.MT_Close_USB()
                self._api.MT_Close_UART()
                self._api.MT_Close_Net()
                self._is_connected = False
                logger.info("已断开控制器连接")
            except Exception as e:
                logger.error(f"断开连接时发生异常: {e}")

    def cleanup(self):
        """清理资源"""
        self.disconnect()
        if self._is_initialized and self._api:
            try:
                self._api.MT_DeInit()
                self._is_initialized = False
                logger.info("API资源已释放")
            except Exception as e:
                logger.error(f"清理资源时发生异常: {e}")

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        self.cleanup()


def create_motor_controller() -> MotorController:
    """创建电机控制器实例的工厂函数

    Returns:
        MotorController: 电机控制器实例
    """
    return MotorController()
