"""
# 基础正弦波处理模块

模块路径：`sweeper400.analyze.basic_sine`

本模块包含与最简单的**单频正弦波**相关的类和函数。
"""

import numpy as np
from typing import Optional, Tuple
from scipy.optimize import curve_fit  # type: ignore
from sweeper400.analyze.my_dtypes import (
    PositiveInt,
    PositiveFloat,
    SamplingInfo,
    Waveform,
)
from sweeper400.logger import get_logger

# 获取模块日志器
logger = get_logger(__name__)


def init_sampling_info(
    sampling_rate: PositiveInt, samples_num: PositiveInt
) -> SamplingInfo:
    """
    标准化地生成采样信息字典

    Args:
        sampling_rate: 采样率，必须为正整数（Hz）
        samples_num: 总采样数，必须为正整数

    Returns:
        sampling_info: 包含采样率和采样数信息的字典

    Examples:
        ```python
        >>> sampling_info = init_sampling_info(PositiveInt(1000), PositiveInt(2048))
        >>> print(sampling_info)
        {'sampling_rate': 1000, 'samples_num': 2048}
        ```
    """
    logger.debug(
        f"创建采样信息: sampling_rate={sampling_rate}Hz, samples_num={samples_num}"
    )

    sampling_info: SamplingInfo = {
        "sampling_rate": sampling_rate,
        "samples_num": samples_num,
    }

    return sampling_info


def sine_wave_vvi(
    sampling_info: SamplingInfo,
    frequency: PositiveFloat,
    amplitude: PositiveFloat = 1.0,
    phase: float = 0.0,
    timestamp: Optional[np.datetime64] = None,
) -> Waveform:
    """
    使用几个简单的参数生成包含单频正弦波时域信号的Waveform对象

    Args:
        sampling_info: 采样信息，包含采样率和采样点数
        frequency: 正弦波频率（Hz）
        amplitude: 正弦波幅值（无单位），默认值为1.0
        phase: 波形的弧度制初始相位（rad），默认值为0.0
        timestamp: 采样开始时间戳，默认值为None

    Returns:
        output_sine_wave: 包含单频正弦波的Waveform对象

    Examples:
        ```python
        >>> sampling_info = {"sampling_rate": 1000.0, "samples_num": 1024}
        >>> sine_wave = sine_wave_vvi(sampling_info, frequency=50.0)
        >>> print(sine_wave.shape)
        (1024,)
        >>> print(sine_wave.sampling_rate)
        1000.0
        ```
    """
    logger.debug(
        f"生成正弦波: frequency={frequency}Hz, amplitude={amplitude}, "
        f"phase={phase}rad, sampling_rate={sampling_info['sampling_rate']}Hz, "
        f"samples_num={sampling_info['samples_num']}, timestamp={timestamp}"
    )

    # 从采样信息中提取参数
    sampling_rate = sampling_info["sampling_rate"]
    samples_num = sampling_info["samples_num"]

    # 生成时间序列
    # 使用 linspace 生成从 0 到 (samples_num-1)/sampling_rate 的时间点
    # endpoint=False 确保不包含最后一个时间点，避免周期性信号的重复
    duration = samples_num / sampling_rate
    time_array = np.linspace(0, duration, samples_num, endpoint=False)

    # 生成正弦波数据
    # y(t) = amplitude * sin(2π * frequency * t + phase)
    sine_data = amplitude * np.sin(2 * np.pi * frequency * time_array + phase)

    # 创建Waveform对象
    output_sine_wave = Waveform(
        input_array=sine_data,
        sampling_rate=sampling_rate,
        timestamp=timestamp,
    )

    logger.debug(f"成功生成正弦波Waveform对象: {output_sine_wave}")

    return output_sine_wave


class SineWaveformVvi:
    """
    # 连续正弦波形生成器类

    使用几个简单的参数多次**连续**生成单频正弦`Waveform`对象。
    初相位会智能更新，因此多次生成的Waveform首尾相接可合成一个无缝的连续波形。

    Examples:
        ```python
        >>> sampling_info = {"sampling_rate": 1000.0, "samples_num": 1024}
        >>> generator = SineWaveformVvi(sampling_info, frequency=50.0)
        >>> wave1 = generator.generate()
        >>> wave2 = generator.generate()
        # wave1和wave2可以无缝连接
        ```
    """

    # 显式声明类型
    _next_timestamp: np.datetime64 | None

    def __init__(
        self,
        sampling_info: SamplingInfo,
        frequency: PositiveFloat,
        amplitude: PositiveFloat = 1.0,
        next_phase: float = 0.0,
        next_timestamp: Optional[np.datetime64] = None,
    ) -> None:
        """
        初始化连续正弦波形生成器

        Args:
            sampling_info: 采样信息，包含采样率和采样点数
            frequency: 正弦波频率（Hz）
            amplitude: 正弦波幅值（无单位），默认值为1.0
            next_phase: 下一次合成Waveform的弧度制初相位（单位rad），默认为0.0
            next_timestamp: 下一次合成Waveform的时间戳，默认为None
        """
        self._sampling_info = sampling_info
        self._frequency = frequency
        self._amplitude = amplitude
        self._next_phase = next_phase
        self._next_timestamp = next_timestamp

        logger.debug(
            f"初始化SineWaveformVvi生成器: "
            f"sampling_info={self._sampling_info}, frequency={self._frequency}Hz, "
            f"amplitude={self._amplitude}, next_phase={self._next_phase}rad, "
            f"next_timestamp={self._next_timestamp}"
        )

    def generate(self) -> Waveform:
        """
        生成连续的正弦波形

        Returns:
            output_sine_wave: 包含单频正弦波的Waveform对象
        """
        logger.debug(
            f"生成连续正弦波: frequency={self._frequency}Hz, amplitude={self._amplitude}, "
            f"使用phase={self._next_phase}rad, timestamp={self._next_timestamp}"
        )

        # 调用sine_wave_vvi函数生成波形
        output_sine_wave = sine_wave_vvi(
            sampling_info=self._sampling_info,
            frequency=self._frequency,
            amplitude=self._amplitude,
            phase=self._next_phase,
            timestamp=self._next_timestamp,
        )

        # 更新下一次生成的相位和时间戳
        self._update_next_parameters(output_sine_wave)

        logger.debug(
            f"成功生成连续正弦波，更新参数: "
            f"next_phase={self._next_phase}rad, next_timestamp={self._next_timestamp}"
        )

        return output_sine_wave

    def _update_next_parameters(self, current_waveform: Waveform) -> None:
        """
        更新下一次生成使用的相位和时间戳

        Args:
            current_waveform: 当前生成的波形
        """
        # 计算下一次的相位，确保波形连续
        # 相位增量 = 2π * frequency * duration
        phase_increment = 2 * np.pi * self._frequency * current_waveform.duration
        self._next_phase = (self._next_phase + phase_increment) % (2 * np.pi)

        # 更新下一次的时间戳
        # 计算时长对应的纳秒数
        duration_ns = int(current_waveform.duration * 1e9)
        # 使用numpy的timedelta64进行时间戳计算，保持纳秒精度
        self._next_timestamp = current_waveform.timestamp + np.timedelta64(
            duration_ns, "ns"
        )


def extract_single_tone_information_vvi(
    input_waveform: Waveform,
    approx_freq: Optional[PositiveFloat] = None,
    error_percentage: PositiveFloat = 5.0,
) -> Tuple[PositiveFloat, PositiveFloat, float]:
    """
    在Waveform对象中搜索指定的频率成分，并返回该成分的详细信息

    Args:
        input_waveform: 目标波形，将在其中搜索单频正弦波
        approx_freq: 搜索的中心频率（Hz）。默认为None，表示在全频率范围内搜索
        error_percentage: 允许的频率误差百分数，无单位。默认值为5.0

    Returns:
        一个包含三个元素的元组，元素分别表示：
        - detected_frequency: 搜索到的单频的精确频率（Hz）
        - detected_amplitude: 搜索到的单频的精确幅值（无单位）
        - detected_phase: 搜索到的单频的精确弧度制初始相位（无单位）

    Examples:
        ```python
        >>> # 生成测试波形
        >>> sampling_info = {"sampling_rate": 1000.0, "samples_num": 1024}
        >>> test_wave = sine_wave_vvi(sampling_info, frequency=50.0, amplitude=2.0)
        >>> freq, amp, phase = extract_single_tone_information_vvi(test_wave, approx_freq=50.0)
        >>> print(f"频率: {freq:.2f}Hz, 幅值: {amp:.2f}, 相位: {phase:.4f}rad")
        ```
    """
    logger.debug(
        f"开始单频信息提取: approx_freq={approx_freq}Hz, "
        f"error_percentage={error_percentage}%, "
        f"waveform_shape={input_waveform.shape}, "
        f"sampling_rate={input_waveform.sampling_rate}Hz"
    )

    # 处理多通道数据，只使用第一个通道
    if input_waveform.ndim == 2:
        waveform_data = input_waveform[0, :]
        logger.debug("检测到多通道数据，使用第一个通道进行分析")
    else:
        waveform_data = input_waveform

    # 获取基本参数
    sampling_rate = input_waveform.sampling_rate
    samples_num = len(waveform_data)
    nyquist_freq = sampling_rate / 2.0

    # 确定搜索频率范围
    if approx_freq is None:
        # 全频率范围搜索
        freq_min = 0.0
        freq_max = nyquist_freq
        logger.debug(f"全频率范围搜索: 0Hz - {nyquist_freq:.2f}Hz")
    else:
        # 根据误差百分比确定搜索范围
        freq_min = approx_freq * (1 - error_percentage / 100.0)
        freq_max_candidate = approx_freq / (1 - error_percentage / 100.0)
        freq_max = min(freq_max_candidate, nyquist_freq)

        logger.debug(
            f"限定频率范围搜索: {freq_min:.2f}Hz - {freq_max:.2f}Hz "
            f"(中心频率: {approx_freq:.2f}Hz, 误差: ±{error_percentage:.1f}%)"
        )

    # 计算FFT进行粗略频率估计
    fft_result = np.fft.fft(waveform_data)
    fft_freqs = np.fft.fftfreq(samples_num, 1.0 / sampling_rate)

    # 只取正频率部分
    positive_freq_mask = fft_freqs >= 0
    fft_result_positive = fft_result[positive_freq_mask]
    fft_freqs_positive = fft_freqs[positive_freq_mask]

    # 计算幅度谱
    fft_magnitude = np.abs(fft_result_positive) / samples_num

    # 在指定频率范围内搜索峰值
    freq_range_mask = (fft_freqs_positive >= freq_min) & (
        fft_freqs_positive <= freq_max
    )
    if not np.any(freq_range_mask):
        raise ValueError(
            f"指定的频率范围 [{freq_min:.2f}, {freq_max:.2f}] Hz 超出了有效范围"
        )

    fft_magnitude_in_range = fft_magnitude[freq_range_mask]
    fft_freqs_in_range = fft_freqs_positive[freq_range_mask]

    # 找到幅度最大的频率点
    max_magnitude_idx = np.argmax(fft_magnitude_in_range)
    coarse_frequency = fft_freqs_in_range[max_magnitude_idx]
    coarse_magnitude = fft_magnitude_in_range[max_magnitude_idx]

    # 使用抛物线拟合进行频率和幅值的精细化估计
    if max_magnitude_idx > 0 and max_magnitude_idx < len(fft_magnitude_in_range) - 1:
        # 取峰值点及其相邻两点进行抛物线拟合
        y1 = fft_magnitude_in_range[max_magnitude_idx - 1]
        y2 = fft_magnitude_in_range[max_magnitude_idx]
        y3 = fft_magnitude_in_range[max_magnitude_idx + 1]

        # 抛物线插值公式
        delta = 0.5 * (y1 - y3) / (y1 - 2 * y2 + y3)
        initial_frequency = coarse_frequency + delta * (
            fft_freqs_in_range[1] - fft_freqs_in_range[0]
        )

        # 重新计算精确幅值
        refined_magnitude = y2 - 0.25 * (y1 - y3) * delta
        initial_amplitude = refined_magnitude * 2.0  # 转换为峰值

        logger.debug(
            f"抛物线拟合改进: 频率={initial_frequency:.6f}Hz, 幅值={initial_amplitude:.6f}"
        )
    else:
        initial_frequency = coarse_frequency
        initial_amplitude = coarse_magnitude * 2.0  # 转换为峰值

        logger.debug(
            f"边界情况，使用粗略估计: 频率={initial_frequency:.6f}Hz, 幅值={initial_amplitude:.6f}"
        )

    logger.debug(
        f"FFT初始估计: 频率={initial_frequency:.6f}Hz, 幅值={initial_amplitude:.6f}"
    )

    # 定义正弦波模型函数
    def sine_model(
        t: np.ndarray, amplitude: float, frequency: float, phase: float
    ) -> np.ndarray:
        """正弦波模型: y = amplitude * sin(2π * frequency * t + phase)"""
        return amplitude * np.sin(2 * np.pi * frequency * t + phase)

    # 构建时间序列
    time_array = np.arange(samples_num) / sampling_rate

    # 使用最小二乘法进行初始相位估计
    cos_term_init = np.cos(2 * np.pi * initial_frequency * time_array)
    sin_term_init = np.sin(2 * np.pi * initial_frequency * time_array)
    design_matrix_init = np.column_stack([cos_term_init, sin_term_init])
    coefficients_init, _, _, _ = np.linalg.lstsq(
        design_matrix_init, waveform_data, rcond=None
    )
    a_init, b_init = coefficients_init
    initial_phase = np.arctan2(a_init, b_init)

    # 设置参数的初始值
    initial_params = [initial_amplitude, initial_frequency, initial_phase]

    # 设置参数边界
    # 幅值：必须为正
    # 频率：在搜索范围内
    # 相位：[-π, π]
    lower_bounds = [0.0, freq_min, -np.pi]
    upper_bounds = [np.inf, freq_max, np.pi]

    try:
        # 使用curve_fit进行非线性最小二乘拟合
        optimal_params, _ = curve_fit(  # type: ignore
            sine_model,
            time_array,
            waveform_data,
            p0=initial_params,
            bounds=(lower_bounds, upper_bounds),
            maxfev=5000,  # 增加最大函数评估次数
        )

        detected_amplitude: float
        detected_frequency: float
        detected_phase: float
        detected_amplitude, detected_frequency, detected_phase = (
            float(optimal_params[0]),  # type: ignore
            float(optimal_params[1]),  # type: ignore
            float(optimal_params[2]),  # type: ignore
        )

        # 计算拟合质量
        fitted_signal = sine_model(
            time_array, detected_amplitude, detected_frequency, detected_phase
        )
        residuals = waveform_data - fitted_signal
        rms_error = np.sqrt(np.mean(residuals**2))

        logger.debug(
            f"curve_fit优化结果: 幅值={detected_amplitude:.6f}, "
            f"频率={detected_frequency:.6f}, 相位={detected_phase:.6f}"
        )
        logger.debug(f"拟合RMS误差: {rms_error:.6f}")

    except Exception as e:
        logger.warning(f"curve_fit优化失败: {e}")
        logger.warning("回退到线性最小二乘法")

        # 回退方案：使用线性最小二乘法，固定频率
        cos_term = np.cos(2 * np.pi * initial_frequency * time_array)
        sin_term = np.sin(2 * np.pi * initial_frequency * time_array)

        # 构建设计矩阵
        design_matrix = np.column_stack([cos_term, sin_term])

        # 使用最小二乘法求解
        coefficients, residuals, _, _ = np.linalg.lstsq(
            design_matrix, waveform_data, rcond=None
        )

        a, b = coefficients

        # 计算幅值和相位
        detected_amplitude = np.sqrt(a**2 + b**2)
        detected_phase = np.arctan2(a, b)
        detected_frequency = initial_frequency

        logger.debug(f"线性最小二乘法结果: a={a:.6f}, b={b:.6f}")
        logger.debug(f"拟合残差平方和: {residuals[0] if len(residuals) > 0 else 'N/A'}")

    logger.debug(
        f"精确结果: 频率={detected_frequency:.6f}Hz, "
        f"幅值={detected_amplitude:.6f}, 相位={detected_phase:.6f}rad"
    )

    return detected_frequency, detected_amplitude, detected_phase
